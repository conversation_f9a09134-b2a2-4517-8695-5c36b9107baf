import { useState, useEffect, createContext, useContext } from 'react';
import { Layout, Menu, Button, theme, Drawer, Space, Typography, Dropdown, Switch, ConfigProvider } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
    LayoutDashboard,
    LogOut,
    UserRound,
    Menu as MenuIcon,
    AlignRight,
    AlignLeft,
    UserPlus,
    Ticket,
    Briefcase,
    Funnel,
    UserCog,
    ShieldCheck,
    UsersRound,
    Users,
    Sun,
    Moon,
    Palette
} from 'lucide-react';
import './index.css';

const { Header, Sider, Content, Footer } = Layout;
const { Text } = Typography;

const ThemeContext = createContext();

export const useTheme = () => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};

const ThemeProvider = ({ children }) => {
    const [isDarkMode, setIsDarkMode] = useState(() => {
        const saved = localStorage.getItem('theme');
        return saved ? saved === 'dark' : false;
    });

    const toggleTheme = () => {
        const newTheme = !isDarkMode;
        setIsDarkMode(newTheme);
        localStorage.setItem('theme', newTheme ? 'dark' : 'light');
    };

    const themeConfig = {
        algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
            colorPrimary: '#6366f1',
            colorBgContainer: isDarkMode ? '#1f2937' : '#ffffff',
            colorBgElevated: isDarkMode ? '#374151' : '#ffffff',
            colorBgLayout: isDarkMode ? '#111827' : '#f8fafc',
            borderRadius: 12,
            boxShadow: isDarkMode
                ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)'
                : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        },
    };

    return (
        <ThemeContext.Provider value={{ isDarkMode, toggleTheme, themeConfig }}>
            <ConfigProvider theme={themeConfig}>
                {children}
            </ConfigProvider>
        </ThemeContext.Provider>
    );
};

const NAVIGATION_CONFIG = {
    '/dashboard': {
        url: 'http://localhost:3002/dashboard',
        port: 3002,
        basename: 'dashboard'
    },
    '/users': {
        url: 'http://localhost:3005/users',
        port: 3005,
        basename: 'users'
    },
    '/users/role': {
        url: 'http://localhost:3005/users/role',
        port: 3005,
        basename: 'users'
    },

    '/users/permission': {
        url: 'http://localhost:3005/users/permission',
        port: 3005,
        basename: 'users'
    },
    '/sales/leads': {
        url: 'http://localhost:3004/sales/leads',
        port: 3004,
        basename: 'sales'
    },
    '/sales/contacts': {
        url: 'http://localhost:3004/sales/contacts',
        port: 3004,
        basename: 'sales'
    },
    '/sales/opportunities': {
        url: 'http://localhost:3004/sales/opportunities',
        port: 3004,
        basename: 'sales'
    },
    '/inventory': {
        url: 'http://localhost:3003/inventory',
        port: 3003,
        basename: 'inventory'
    },
    '/tickets': {
        url: 'http://localhost:3006/tickets',
        port: 3006,
        basename: 'tickets'
    }
};

const AdminLayoutContent = ({ children }) => {
    const { isDarkMode, toggleTheme } = useTheme();
    const [collapsed, setCollapsed] = useState(false);
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [themeDrawerVisible, setThemeDrawerVisible] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [openKeys, setOpenKeys] = useState(['/sales']);
    const navigate = useNavigate();
    const location = useLocation();
    const user = JSON.parse(localStorage.getItem('user') || '{}');

    const {
        token: { colorBgContainer, borderRadiusLG, colorBgLayout, colorBgElevated },
    } = theme.useToken();



    const menuItems = [
        {
            key: '/dashboard',
            icon: <LayoutDashboard size={16} />,
            label: 'Dashboard',
        },
        // {
        //     key: '/users',
        //     icon: <User size={16} />,
        //     label: 'User',
        // },
        {
            key: "/users",
            icon: <UsersRound size={16} />,
            label: "Users Management",
            children: [
                {
                    key: "/users",
                    label: "Users",
                    icon: <Users size={16} />,
                },
                {
                    key: "/users/role",
                    label: "Roles",
                    icon: <UserCog size={16} />,
                },
                {
                    key: "/users/permission",
                    label: "Permissions",
                    icon: <ShieldCheck size={16} />,
                },
            ],
        },
        {
            key: '/sales',
            icon: <Briefcase size={16} />,
            label: 'Sales',
            children: [
                {
                    key: '/sales/leads',
                    label: 'Leads',
                    icon: <Funnel size={16} />,
                },
                {
                    key: '/sales/contacts',
                    label: 'Contacts',
                    icon: <UserPlus size={16} />,
                },
                // {
                //     key: '/sales/opportunities',
                //     label: 'Opportunities',
                //     icon: <Boxes size={16} />,
                // }
            ]
        },
        {
            key: '/tickets',
            icon: <Ticket size={16} />,
            label: 'Ticket',
        },
    ];

    const handleMenuClick = ({ key }) => {
        const config = NAVIGATION_CONFIG[key];
        const currentPort = window.location.port;

        if (config) {
            if (config.port.toString() !== currentPort) {
                const token = localStorage.getItem('token');
                const url = token ? `${config.url}?token=${token}` : config.url;
                window.location.href = url;
            } else {
                const routePath = key.replace(`/${config.basename}`, '') || '/';
                navigate(routePath);
            }
        } else {
            navigate(key);
        }

        if (isMobile) {
            setDrawerVisible(false);
        }
    };

    const getSelectedKeys = () => {
        const currentPort = window.location.port;
        const currentPath = window.location.pathname;

        for (const [key, config] of Object.entries(NAVIGATION_CONFIG)) {
            if (config.port.toString() === currentPort) {
                if (key === "/dashboard" && currentPath.includes("dashboard")) return ["/dashboard"];
                if (key === "/users" && currentPath === "/users") return ["/users"];
                if (key === "/users/role" && currentPath.includes("/role")) return ["/users/role"];
                if (key === "/users/permission" && currentPath.includes("/permission")) return ["/users/permission"];
                if (key === "/tickets" && currentPath.includes("tickets")) return ["/tickets"];
                if (key === "/inventory" && currentPath.includes("inventory")) return ["/inventory"];

                if (key === "/sales/leads" && currentPath.includes("/leads")) return ["/sales/leads"];
                if (key === "/sales/contacts" && currentPath.includes("/contacts")) return ["/sales/contacts"];
                if (key === "/sales/opportunities" && currentPath.includes("/opportunities")) return ["/sales/opportunities"];
            }
        }

        return [location.pathname];
    };


    useEffect(() => {
        const currentPath = window.location.pathname;
        if (currentPath.includes('/leads') || currentPath.includes('/contacts') || currentPath.includes('/opportunities')) {
            setOpenKeys(['/sales']);
        }
    }, [location.pathname]);

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 992);
        };

        handleResize();

        window.addEventListener('resize', handleResize);

        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const toggleSider = () => {
        setCollapsed(!collapsed);
    };

    const showDrawer = () => {
        setDrawerVisible(true);
    };

    const onCloseDrawer = () => {
        setDrawerVisible(false);
    };

    const handleLogout = () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = 'http://localhost:3001/auth';
    };

    const showThemeDrawer = () => {
        setThemeDrawerVisible(true);
    };

    const onCloseThemeDrawer = () => {
        setThemeDrawerVisible(false);
    };

    const userMenuItems = [
        {
            key: 'profile',
            label: 'Profile',
            icon: <UserRound size={16} />,
        },
        {
            key: 'theme',
            label: 'Theme Settings',
            icon: <Palette size={16} />,
            onClick: showThemeDrawer,
        },
        {
            type: 'divider',
        },
        {
            key: 'logout',
            label: 'Logout',
            icon: <LogOut size={16} />,
            onClick: handleLogout,
        },
    ];

    return (
        <Layout style={{
            minHeight: '100vh',
            background: colorBgLayout,
        }}>
            {!isMobile && (
                <Sider
                    theme={isDarkMode ? 'dark' : 'light'}
                    collapsed={collapsed}
                    onCollapse={setCollapsed}
                    breakpoint="lg"
                    width={280}
                    collapsedWidth={80}
                    style={{
                        overflow: 'auto',
                        height: '100vh',
                        position: 'fixed',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        background: colorBgElevated,
                        borderRight: `1px solid ${isDarkMode ? '#374151' : '#e5e7eb'}`,
                        boxShadow: '2px 0 8px rgba(0, 0, 0, 0.1)',
                    }}
                >
                    <div className="logo-container" style={{
                        height: 64,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: collapsed ? 'center' : 'flex-start',
                        padding: collapsed ? '0' : '0 24px',
                        borderBottom: `1px solid ${isDarkMode ? '#374151' : '#e5e7eb'}`,
                        background: colorBgElevated,
                    }}>
                        {collapsed ? (
                            <div style={{
                                width: 40,
                                height: 40,
                                borderRadius: '12px',
                                background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: '18px',
                            }}>
                                A
                            </div>
                        ) : (
                            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                                <div style={{
                                    width: 40,
                                    height: 40,
                                    borderRadius: '12px',
                                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    color: 'white',
                                    fontWeight: 'bold',
                                    fontSize: '18px',
                                }}>
                                    A
                                </div>
                                <Text strong style={{
                                    fontSize: '20px',
                                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                    backgroundClip: 'text',
                                }}>
                                    Accord CRM
                                </Text>
                            </div>
                        )}
                    </div>
                    <div style={{ padding: '16px 8px' }}>
                        <Menu
                            selectedKeys={getSelectedKeys()}
                            openKeys={openKeys}
                            onOpenChange={setOpenKeys}
                            mode="inline"
                            items={menuItems}
                            onClick={handleMenuClick}
                            style={{
                                border: 'none',
                                background: 'transparent',
                            }}
                        />
                    </div>
                </Sider>
            )}

            {isMobile && (
                <Drawer
                    title={
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                            <div style={{
                                width: 32,
                                height: 32,
                                borderRadius: '8px',
                                background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: '16px',
                            }}>
                                A
                            </div>
                            <Text strong style={{ fontSize: '18px' }}>Accord CRM</Text>
                        </div>
                    }
                    placement="left"
                    closable={true}
                    onClose={onCloseDrawer}
                    open={drawerVisible}
                    width={280}
                    styles={{
                        body: {
                            padding: '16px 8px',
                            background: colorBgElevated,
                        },
                        header: {
                            background: colorBgElevated,
                            borderBottom: `1px solid ${isDarkMode ? '#374151' : '#e5e7eb'}`,
                        }
                    }}
                >
                    <Menu
                        theme={isDarkMode ? 'dark' : 'light'}
                        selectedKeys={getSelectedKeys()}
                        openKeys={openKeys}
                        onOpenChange={setOpenKeys}
                        mode="inline"
                        items={menuItems}
                        onClick={handleMenuClick}
                        style={{
                            border: 'none',
                            background: 'transparent',
                        }}
                    />
                </Drawer>
            )}

            <Layout
                style={{
                    marginLeft: isMobile ? 0 : (collapsed ? 80 : 280),
                    transition: 'margin-left 0.3s ease',
                    background: colorBgLayout,
                }}
                className="site-layout"
            >
                <Header
                    style={{
                        padding: '0 24px',
                        background: colorBgContainer,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        position: 'sticky',
                        top: 0,
                        zIndex: 10,
                        borderBottom: `1px solid ${isDarkMode ? '#374151' : '#e5e7eb'}`,
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                        height: 64,
                    }}
                >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Button
                            type="text"
                            icon={isMobile ? <MenuIcon size={18} /> : (collapsed ? <AlignLeft size={18} /> : <AlignRight size={18} />)}
                            onClick={isMobile ? showDrawer : toggleSider}
                            style={{
                                fontSize: '16px',
                                width: 48,
                                height: 48,
                                borderRadius: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                transition: 'all 0.2s ease',
                            }}
                            className="header-button"
                        />
                    </div>

                    <Space size="middle">
                        <Dropdown
                            menu={{ items: userMenuItems }}
                            placement="bottomRight"
                            trigger={['click']}
                        >
                            <Button
                                type="text"
                                style={{
                                    height: 48,
                                    borderRadius: '12px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    padding: '0 16px',
                                    transition: 'all 0.2s ease',
                                }}
                                className="user-button"
                            >
                                <div style={{
                                    width: 32,
                                    height: 32,
                                    borderRadius: '8px',
                                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    color: 'white',
                                    fontSize: '14px',
                                    fontWeight: 'bold',
                                }}>
                                    {(user?.name || user?.email || 'Demo').charAt(0).toUpperCase()}
                                </div>
                                <span style={{ fontWeight: 500 }}>
                                    {user?.name || user?.email || 'Demo'}
                                </span>
                            </Button>
                        </Dropdown>
                    </Space>
                </Header>

                <Content
                    style={{
                        margin: '24px',
                        overflow: 'initial',
                        minHeight: 'calc(100vh - 112px)',
                    }}
                >
                    <div
                        style={{
                            // padding: '32px',
                            minHeight: 'calc(100vh - 176px)',
                            background: colorBgContainer,
                            borderRadius: borderRadiusLG,
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                            border: `1px solid ${isDarkMode ? '#374151' : '#e5e7eb'}`,
                        }}
                    >
                        {children}
                    </div>
                </Content>

                <Footer style={{
                    textAlign: 'center',
                    background: colorBgLayout,
                    borderTop: `1px solid ${isDarkMode ? '#374151' : '#e5e7eb'}`,
                    color: isDarkMode ? '#9ca3af' : '#6b7280',
                    padding: '16px 24px',
                }}>
                    Copyright ©{new Date().getFullYear()}. All rights reserved.
                </Footer>
            </Layout>

            <Drawer
                title="Theme Settings"
                placement="right"
                closable={true}
                onClose={onCloseThemeDrawer}
                open={themeDrawerVisible}
                width={320}
                styles={{
                    body: {
                        padding: '24px',
                        background: colorBgElevated,
                    },
                    header: {
                        background: colorBgElevated,
                        borderBottom: `1px solid ${isDarkMode ? '#374151' : '#e5e7eb'}`,
                    }
                }}
            >
                <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
                    <div>
                        <Text strong style={{ fontSize: '16px', marginBottom: '12px', display: 'block' }}>
                            Appearance
                        </Text>
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            padding: '16px',
                            background: colorBgContainer,
                            borderRadius: '12px',
                            border: `1px solid ${isDarkMode ? '#374151' : '#e5e7eb'}`,
                        }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                                {isDarkMode ? <Moon size={20} /> : <Sun size={20} />}
                                <div>
                                    <Text strong>{isDarkMode ? 'Dark Mode' : 'Light Mode'}</Text>
                                    <div style={{ fontSize: '12px', color: isDarkMode ? '#9ca3af' : '#6b7280' }}>
                                        {isDarkMode ? 'Dark theme is active' : 'Light theme is active'}
                                    </div>
                                </div>
                            </div>
                            <Switch
                                checked={isDarkMode}
                                onChange={toggleTheme}
                                checkedChildren={<Moon size={12} />}
                                unCheckedChildren={<Sun size={12} />}
                            />
                        </div>
                    </div>
                </div>
            </Drawer>
        </Layout>
    );
};

const AdminLayout = ({ children }) => {
    return (
        <ThemeProvider>
            <AdminLayoutContent>{children}</AdminLayoutContent>
        </ThemeProvider>
    );
};

export default AdminLayout;
