* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ant-menu-item,
.ant-menu-submenu-title {
  border-radius: 8px !important;
  margin: 4px 0 !important;
  padding: 0 16px !important;
  height: 44px !important;
  line-height: 44px !important;
  transition: all 0.2s ease !important;
}

.ant-menu-item:hover,
.ant-menu-item-active,
.ant-menu-submenu-title:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%) !important;
  color: #6366f1 !important;
  transform: translateX(4px);
  border-left: 3px solid #6366f1;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15) !important;
}

.ant-menu-item-selected {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
  border-left: 3px solid #a855f7;
}

.ant-menu-item-selected:hover {
  background: linear-gradient(135deg, #5b5bd6 0%, #7c3aed 100%) !important;
  color: white !important;
  transform: translateX(4px);
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4) !important;
}

.ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: #6366f1 !important;
  font-weight: 600 !important;
}

.ant-menu-inline .ant-menu-item {
  width: calc(100% - 8px) !important;
  margin-left: 4px !important;
}

.ant-menu-submenu-inline > .ant-menu-submenu-title {
  width: calc(100% - 8px) !important;
  margin-left: 4px !important;
}

.ant-menu-sub.ant-menu-inline {
  background: transparent !important;
}

.ant-menu-sub .ant-menu-item {
  padding-left: 32px !important;
  margin-left: 8px !important;
  width: calc(100% - 16px) !important;
}

.header-button:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%) !important;
  color: #6366f1 !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15) !important;
}

.user-button:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.2) !important;
}

.ant-drawer-header {
  padding: 16px 24px !important;
}

.ant-drawer-body {
  padding: 0 !important;
}

.ant-layout-sider-trigger {
  display: none !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  border-left: 3px solid #a855f7;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4) !important;
}

.ant-menu-dark .ant-menu-item:hover,
.ant-menu-dark .ant-menu-submenu-title:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%) !important;
  color: #c7d2fe !important;
  border-left: 3px solid #6366f1;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2) !important;
}

@media (max-width: 768px) {
  .ant-layout-content {
    margin: 16px !important;
  }

  .ant-layout-content > div {
    padding: 20px !important;
  }
}

@media (max-width: 576px) {
  .ant-layout-content {
    margin: 8px !important;
  }

  .ant-layout-content > div {
    padding: 16px !important;
  }

  .ant-layout-header {
    padding: 0 16px !important;
  }
}
